<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * English language pack for Offer Manager
 *
 * @package    local_offermanager
 * @category   string
 * @copyright  2025 2015 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();


$string['pluginname'] = 'Offer Manager';
$string['offermanager:manage'] = 'Manage Offer Manager';
$string['offermanager:viewparticipants'] = 'Allows viewing the enrolled users screen';
$string['offermanager:manageparticipants'] = 'Allows managing the enrolled users screen';

$string['manage'] = 'Manage Offers';
$string['offer'] = 'Offer';
$string['offers'] = 'Offers';
$string['offer_created'] = 'Offer created successfully!';
$string['offer_updated'] = 'Offer updated successfully!';
$string['offer_deleted'] = 'Offer deleted successfully!';
$string['offer_courses'] = 'Offer Course';
$string['offer_course_plural'] = 'Offer Courses';
$string['offer_audiences'] = 'Offer Target Audience';
$string['offer_audiences_plural'] = 'Offer Target Audiences';
$string['offer_class'] = 'Class';
$string['offer_class_plural'] = 'Classes';
$string['notaccessible_title'] = 'Class not accessible';
$string['notaccessible_heading'] = 'Class not accessible';
$string['notaccessible_message'] = 'The class "{$a->classname}" is not accessible because the minimum number of {$a->minrequired} enrollments has not yet been reached.<br> Contact your system administration for more information.';

$string['situation:enroled'] = 'Enroled';
$string['situation:in_progress'] = 'In Progress';
$string['situation:approved'] = 'Approved';
$string['situation:completed'] = 'Completed';
$string['situation:user_canceled'] = 'Canceled by User';
$string['situation:admin_canceled'] = 'Canceled by Admin';
$string['situation:failed'] = 'Failed';
$string['situation:not_completed'] = 'Not Completed';
$string['situation:abandoned'] = 'Abandoned';
$string['situation:unknown'] = 'Unknown Situation';

$string['event:offercreated'] = 'Offer Created';
$string['event:offerupdated'] = 'Offer Updated';
$string['event:offerdeleted'] = 'Offer Deleted';
$string['event:offeractivated'] = 'Offer Activated';
$string['event:offerinactivated'] = 'Offer Deactivated';
$string['event:offeraudiencecreated'] = 'Relationship between offer and audience created';
$string['event:offeraudienceupdated'] = 'Relationship between offer and audience updated';
$string['event:offeraudiencedeleted'] = 'Relationship between offer and audience deleted';
$string['event:offercoursecreated'] = 'Relationship between offer and course created';
$string['event:offercourseupdated'] = 'Relationship between offer and course updated';
$string['event:offercoursedeleted'] = 'Relationship between offer and course deleted';
$string['event:offercourseactivated'] = 'Relationship between offer and course activated';
$string['event:offercourseinactivated'] = 'Relationship between offer and course deactivated';
$string['event:offerclasscreated'] = 'Class created';
$string['event:offerclassupdated'] = 'Class deleted';
$string['event:offerclassactivated'] = 'Class activated';
$string['event:offerclassinactivated'] = 'Class deactivated';
$string['event:offerclassdeleted'] = 'Class updated';
$string['event:offer_audiences_updated'] = 'Audience group updated';
$string['event:offerclassteachersupdated'] = 'Theacher of class {$a->} was updated';
$string['event:offerclassaccessible'] = 'Class accessible';
$string['event:offerclasscyclestarted'] = 'Offer class operational cycle started';
$string['event:offerclasscyclefinished'] = 'Offer class operational cycle finished';
$string['event:offer_user_enrol_enroled'] = 'Offer user enrolment enroled';
$string['event:offer_user_enrol_in_progress'] = 'Offer user enrolment in progress';
$string['event:offer_user_enrol_canceled'] = 'Offer user enrolment canceled';
$string['event:offer_user_enrol_approved'] = 'Offer user enrolment approved';
$string['event:offer_user_enrol_completed'] = 'Offer user enrolment completed';
$string['event:offer_user_enrol_failed'] = 'Offer user enrolment failed';
$string['event:offer_user_enrol_not_completed'] = 'Offer user enrolment not completed';
$string['event:offer_user_enrol_abandoned'] = 'Offer user enrolment abandoned';
$string['event:offer_user_enrol_created'] = 'Offer user enrolment created';
$string['event:offer_user_enrol_edited'] = 'Offer user enrolment edited';
$string['event:offer_user_enrol_deleted'] = 'Offer user enrolment deleted';
$string['event:user_enrolment_reenroled'] = 'Offer user enrolment reenrolment';

$string['message:class_created'] = 'Class {$a->name} was successfully created.';
$string['message:enrolment_success'] = 'User successfully enrolled in the class';
$string['message:class_updated'] = 'Class {$a->name} successfully updated.';
$string['message:class_deleted'] = 'Class {$a->name} successfully deleted.';
$string['message:enrolment_failed'] = 'Error enrolling user';
$string['message:enrolment_failed_detailed'] = 'Unable to complete enrollment. Please try again or contact technical support for assistance.';
$string['message:self_unenrol'] = 'By canceling your enrollment, your progress up to this point will be saved and you will no longer have access to the course content.';
$string['message:cancel_reason'] = 'Cancellation reason';
$string['message:cancel_reason_help'] = 'Enter here the reason for canceling the enrollment';
$string['message:offer_user_enrol_self_canceled'] = "Your enrollment in the class was successfully canceled.</br> Please wait as you will be redirected to the main page.";
$string['message:self_unenrol_success'] = 'Your enrollment has been successfully cancelled.';
$string['message:reason_cancel_previous_enrolment'] = 'The enrolment cancellation in the class was carried out because user {$a->adminid} enrolled user {$a->userid} in class {$a->newofferclassid}.';
$string['message:offer_user_enrol_canceled'] = 'The enrollment of {$a->username} in the class {$a->offerclassname} was successfully canceled.';

$string['error:user_not_found'] = 'User with ID {$a} not found';
$string['error:offer_class_not_found'] = 'Offer class not found';
$string['error:enrol_instance_not_found'] = 'Enrollment instance not found';
$string['error:user_already_enrolled'] = 'User {$a} is already enrolled in this class';
$string['error:accessdenied'] = 'You do not have permission to access the plugin ' . $string['pluginname'];
$string['error:cannot_activate_offer'] = 'Unable to activate the offer. Please make sure all courses have classes configured and at least one target audience is assigned.';
$string['error:offer_already_inactive'] = 'The offer is already inactive';
$string['error:offer_already_active'] = 'The offer is already active';
$string['error:cannot_delete_offer'] = 'This offer cannot be deleted.';
$string['error:cannot_delete_offer_course'] = 'This course cannot be removed from the offer.';
$string['error:cannot_created_offer_class'] = 'The class could not be created';
$string['error:cannot_delete_offer_class'] = 'This class cannot be removed from the offer';
$string['error:duplicate_offer_course'] = 'The offer and course combination already exists.';
$string['error:duplicate_offer_audience'] = 'The offer and audience combination already exists.';
$string['error:offer_audience_not_found'] = 'The relationship between offer and audience was not found.';
$string['error:audience_not_found'] = 'The audience was not found.';
$string['error:invalid_audience_ids'] = 'The submitted audience list is not valid.';
$string['error:offer_course_not_found'] = 'The relationship between offer and course was not found.';
$string['error:offer_course_already_active'] = 'The relationship between offer and course is already activated';
$string['error:offer_course_already_inactive'] = 'The relationship between offer and course is already deactivated';
$string['error:offer_class_already_active'] = 'The class is already activated';
$string['error:offer_class_already_inactive'] = 'The class is already deactivated';
$string['error:course_not_found'] = 'Course not found.';
$string['error:enrol_plugin_not_found'] = 'Registration method not found.';
$string['error:offer_not_found'] = 'Offer not found';
$string['error:class_not_found'] = 'Class not found';
$string['error:user_enrol_not_found'] = 'User Enrolment not found';
$string['error:invalid_situation'] = 'Invalid enrollment situation';
$string['error:offer_name_required'] = 'Offer name required';
$string['error:offer_status_required'] = 'Offer name required';
$string['error:enrolid_doesnt_exist'] = 'Enrollment instance does not exist';
$string['error:enrolid_already_exists'] = 'Enrollment instance already has a relationship with a course in an offering';
$string['error:offer_class_already_finished'] = 'The class has already finished';
$string['error:offer_class_already_started'] = 'The class with id {$a} has already started';
$string['error:cannot_view_course'] = 'You cannot view this course';
$string['error:user_not_enrolled'] = 'User is not enrolled in this class';
$string['error:invalid_userid'] = 'Invalid user ID';
$string['error:role_not_found'] = 'Teacher role not found';
$string['error:update_teacher_error'] = 'Something went wrong when updating teachers';
$string['error:cannot_update_offer_class'] = 'Cannot update class';
$string['error:duplicate_different_offer'] = 'Duplication is only allowed between courses of the same offer.';
$string['error:duplicate_same_course'] = 'Duplication cannot occur in the same course as the original class.';
$string['error:cannot_create_enrol_instance'] = 'Could not create the new enrolment instance in the target course.';
$string['error:classname_required'] = 'Class name is required.';
$string['error:startdate_required'] = 'Class start date is required.';
$string['error:offercourseid_required'] = 'Offer course ID is required.';
$string['error:minusers_numeric'] = 'Minimum number of users must be a number.';
$string['error:maxusers_numeric'] = 'Maximum number of users must be a number.';
$string['error:enddate_required'] = 'Class end date is required.';
$string['error:enddate_before_startdate'] = 'Class end date must be after the start date.';
$string['error:preregistrationstartdate_required'] = 'Pre-enrollment start date is required.';
$string['error:preregistrationenddate_required'] = 'Pre-enrollment end date is required.';
$string['error:preregistrationenddate_before_preregistrationstartdate'] = 'Pre-enrollment end date must be after the start date.';
$string['error:maxusers_less_than_minusers'] = 'Maximum number of users must be greater than or equal to the minimum number of users.';
$string['error:enrolperiod_numeric'] = 'The conclusion range must be a number.';
$string['extensiondaysavailabie_required'] = 'Number of days before end date for extension is required.';
$string['extensionmaxrequests_required'] = 'Maximum number of allowed extensions is required.';
$string['extensionmaxrequests_numeric'] = 'Maximum number of allowed extensions must be a number.';
$string['error:field_not_mapped'] = 'Field not mapped';
$string['error:user_enrol_not_canceled'] = 'The enrol was not canceled';
$string['error:cancel_reason_cannot_be_empty'] = 'The cancellation reason cannot be empty on registration';
$string['error:offerclass_not_active'] = 'Class is not active';
$string['error:offercourse_not_active'] = 'Course in Offer is not active';
$string['error:offer_not_active'] = 'Offer is not active';
$string['error:reached_max_users'] = 'Maximum number of enrollments reached.';
$string['error:outside_preenrolment'] = 'Outside the pre-enrol period';
$string['error:outside_classperiod'] = 'Outside the class period';
$string['error:preenrol_starts_on'] = 'Enrolments at in this class are not available. It is supposed to start on {$a->startdate}.';
$string['error:preenrol_starts_between'] = 'Enrolments at in this class are not available. It is supposed to open on {$a->startdate} and close on {$a->enddate}.';
$string['error:user_has_active_enrolment'] = 'The user {$a} already has an active enrolment in this course through one of the classes';
$string['error:operational_cycle_notfound'] = 'Class with id {$a} not found. Ignoring task.';
$string['error:operational_cycle_fail'] = 'Failed to update operational cycle. Aborting.';
$string['error:invalid_situation_transition'] = 'Invalid situation transition from "{$a->current}" to "{$a->new}"';
$string['error:formdata_missing'] = 'Form data is missing.';
$string['error:invalid_offer_user_enrol_for_cancel'] = 'Invalid offer user enrolment for cancel.';
$string['error:cannot_cancel_offer_user_enrol'] = 'Cannot cancel offer user enrolment.';
$string['error:self_unenrol_failed'] = 'An error occurred while trying to cancel your enrollment. Please try again or contact support.';
$string['error:delete_user_enrolment_fail'] =  'Fail on delete user_enrolments';
$string['invaliddateformat'] = 'Invalid date format: {$a}. Please use the format YYYY-MM-DD.';
$string['error:invalid_orderby_field'] = 'Invalid orderby value: {$a}';

$string['config:enableplugin'] = 'Enable plugin';
$string['config:enableplugin_desc'] = 'Enables/disables the plugin in the environment. When disabling, all classes of the created offers will be inactivated.';
$string['config:enableplatformenrol'] = 'Enable platform registration methods';
$string['config:enableplatformenrol_desc'] = 'When disabling this option, all registration methods available on the platform will be disabled, making only the Offer Manager methods available.';
$string['config:enabletypeoptions'] = 'Enable filter by Offer Type';
$string['config:enabletypeoptions_desc'] = 'Enables a filter by Offer Type on the Offer Manager home page.';
$string['config:typeoptions_help'] = 'Enabling this option will create a choice field within the offer instance containing the options defined in the text editor below. This setting does not bind specific rules, but helps identify the offer type. Additionally, it also adds a filter called \'Offer Type\' to the main page of the Offer Manager.';
$string['config:optionsmenu'] = 'Offer Types';
$string['config:typeoptions'] = 'Offer Type Filter Options';
$string['config:typeoptions_desc'] = 'Enter the values ​​that will be displayed in the Offer Type filter, one per line.';
$string['config:defaulttypeoption'] = 'Default value';
$string['config:defaulttypeoption_desc'] = 'Default value to be displayed in the Offer Type field.';

$string['task:turnclassesaccessible'] = 'Make classes automatically accessible';
$string['task:syncenrolplugins'] = 'Sync dependent enrolment plugins';
$string['task:operational_cycle_start'] = 'Executing operational cycle update for class {$a->id} (attempt {$a->attempt}).';
$string['task:operational_cycle_success'] = 'Class operational cycle updated successfully.';
$string['task:operational_cycle_retry'] = 'Failed to update operational cycle. Retrying in 10 minutes (attempt {$a->attempt}/{$a->max}).';

$string['page:self_unenrol_title'] = 'Cancel Enrollment';
$string['page:self_unenrol_heading'] = 'Cancel Enrollment in class {$a}';
$string['confirm:self_unenrol'] = 'Are you sure you want to cancel your enrollment? You will lose access to the course content.';

// Reenrolment strings
$string['reenrolme'] = 'Re-enrollment in class {$a->classname}';
$string['reenrol_info'] = 'When re-enrolling, the user\'s progress within the course will be migrated to history and progress data will be erased.';
$string['reenrolme_button'] = 'Re-enroll me';
$string['error:cannot_reenrol'] = 'You cannot enroll in this class';
$string['error:method_not_implemented'] = 'Error: The required method \'{$a}\' is not implemented in the class using the trait.';
$string['error:enrol_already_archived'] = 'The user enrolment record is already archived.';
$string['error:cannot_create_history_record'] = 'Could not create history record for user enrolment.';
$string['error:user_enrolment_not_found'] = 'User enrolment record not found or user ID is missing.';
$string['message:reenrolment_success'] = 'You have reenroled in course {$a}!';
$string['message:general_reenrolment_success'] = 'User {$a->userfullname} was reenroled in class {$a->classname}';
$string['message:general_reenrolment_failed'] = 'Something went wrong when reenrolling User {$a->userfullname} in class {$a->classname}';
// Extension strings
$string['extendenrol'] = 'Extend enrol';
$string['nopermissions'] = 'You do not have permissions to perform this action.';
$string['error:invalid_offer_user_enrol'] = 'Invalid offer user enrolment.';
$string['enableextension'] = 'Enable Extension';
$string['enableextension_desc'] = 'Enable the enrollment extension feature for this class.';
$string['extensionperiod'] = 'Extension Period (days)';
$string['extensionperiod_desc'] = 'Number of days to add to the user\'s enrollment end date upon extension.';
$string['extensiondaysavailable'] = 'Days before end date to show extension option';
$string['extensiondaysavailable_desc'] = 'Number of days before the enrollment end date that the extension option will be displayed to the user.';
$string['extensionmaxrequests'] = 'Maximum Extension Requests';
$string['extensionmaxrequests_desc'] = 'Maximum number of times a user can request an extension for this enrollment.';
$string['extensionallowedsituations'] = 'Allowed Situations for Extension';
$string['extensionallowedsituations_desc'] = 'Select the enrollment situations that are allowed to request an extension.';

// Extension error messages
$string['error:extension_class_not_found'] = 'Extension not possible: Class not found.';
$string['error:extension_invalid_timeend'] = 'Extension not possible: Your already reached the limit of time to complete the course.';
$string['error:extension_invalid_config'] = 'Extension not possible: Extension configuration is incomplete or invalid for this class.';
$string['error:extension_invalid_situation'] = 'Extension not possible: Your current enrollment situation ("{$a}") is not allowed for extension.';
$string['error:extension_limit_reached'] = 'Extension not possible: You have reached the maximum number of allowed extensions.';
$string['error:cannot_extend_enrol'] = 'Cannot extend enrollment';
$string['error:extension_cannot_process'] = 'Cannot process extension request: {$a}';
$string['error:extension_invalid_period'] = 'Extension not possible: The extension period is invalid or not configured.';

// Extension success and warning messages
$string['success:extension_granted'] = 'Your enrollment has been extended successfully.';
$string['warning:extension_limited_by_class_enddate'] = 'Your enrollment has been extended, but it was limited to match the class end date.';

$string['event:user_enrolment_extended'] = 'User enrollment extended';

$string['extension_reason_default'] = 'Enrollment extended by user request.';

$string['message:offer_user_enrol_self_extended'] = 'Your enrollment has been extended successfully.';
$string['error:cannot_extend_offer_user_enrol'] = 'Cannot extend offer user enrolment.';

// Extension form strings
$string['message:extend_enrol'] = 'By extending your enrollment, you will have more time to complete the course. Please confirm that you want to extend your enrollment.';
$string['message:extend_reason'] = 'Reason for extension';
$string['message:extend_reason_help'] = 'Please provide a reason for requesting an extension.';
$string['message:extension_new_date'] = 'If extended, your enrollment will be valid until: <strong>{$a->date}</strong>';
$string['message:extension_limited_warning'] = '<strong>Note:</strong> The extension will be limited to match the class end date ({$a->date}).';
$string['message:extension_count'] = 'This will be extension {$a->current} of {$a->max} allowed.';

$string['enrolperiod'] = 'Enrol Period';
$string['enrol_period_format'] = 'from {$a->startdate} to {$a->enddate}';
$string['enrol_period_format_start'] = 'Starts on {$a->startdate}';
$string['class_format_start'] = 'Class starts on {$a->startdate}';

$string['startdate'] = 'Class start date';